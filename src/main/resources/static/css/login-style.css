/* === Variables === */
:root {
    --primary-color: #001660; /* Bleu foncé */
    --primary-light: #002080;
    --primary-dark: #001040;
    --primary-gradient: linear-gradient(135deg, #001660 0%, #002080 50%, #003399 100%);
    --accent-color: #FF5722; /* Orange */
    --accent-light: #FF7043;
    --accent-gradient: linear-gradient(135deg, #FF5722 0%, #FF7043 50%, #FF8A65 100%);
    --text-color: #2d3748;
    --text-light: #718096;
    --bg-color: #f0f2f5;
    --bg-gradient: linear-gradient(135deg, #f0f2f5 0%, #e8ecf0 50%, #dde4ea 100%);
    --white: #ffffff;
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --border-color: #e2e8f0;
    --shadow-color: rgba(0, 22, 96, 0.15);
    --shadow-elegant: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
    --success-color: #38a169;
    --error-color: #e53e3e;
    --google-color: #DB4437;
    --github-color: #333333;
}

/* === Global Reset and Base Styles === */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

body {
    background-color: #f0f2f5;
    color: var(--text-color);
    position: relative;
    overflow: hidden;
    height: 100vh;
}

/* Cercles décoratifs */
body::before,
body::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    z-index: -1;
}

/* Grand cercle bleu en haut à droite */
body::before {
    width: 450px;
    height: 450px;
    background-color: rgba(0, 22, 96, 0.03);
    top: -150px;
    right: -100px;
    filter: blur(80px);
}

/* Cercle orange en bas à gauche */
body::after {
    width: 400px;
    height: 400px;
    background-color: rgba(255, 87, 34, 0.03);
    bottom: -150px;
    left: -150px;
    filter: blur(80px);
}

/* Cercles additionnels */
.bg-circle-1, .bg-circle-2, .bg-circle-3, .bg-circle-4 {
    position: absolute;
    border-radius: 50%;
    z-index: -1;
}

.bg-circle-1 {
    width: 300px;
    height: 300px;
    background-color: rgba(0, 22, 96, 0.02);
    top: 10%;
    right: 25%;
    filter: blur(70px);
}

.bg-circle-2 {
    width: 200px;
    height: 200px;
    background-color: rgba(255, 87, 34, 0.02);
    top: 65%;
    right: 15%;
    filter: blur(60px);
}

.bg-circle-3 {
    width: 350px;
    height: 350px;
    background-color: rgba(0, 22, 96, 0.02);
    bottom: 5%;
    left: 20%;
    filter: blur(70px);
}

.bg-circle-4 {
    width: 180px;
    height: 180px;
    background-color: rgba(255, 87, 34, 0.02);
    top: 20%;
    left: 15%;
    filter: blur(50px);
}

/* Cercles animés */
.animated-circle {
    position: absolute;
    border-radius: 50%;
    z-index: -1;
    opacity: 0.5;
}

.blue-circle-1 {
    width: 80px;
    height: 80px;
    background-color: rgba(0, 22, 96, 0.15);
    top: 15%;
    right: 10%;
    animation: float 8s ease-in-out infinite;
}

.blue-circle-2 {
    width: 40px;
    height: 40px;
    background-color: rgba(0, 22, 96, 0.2);
    bottom: 20%;
    right: 20%;
    animation: floatRotate 12s ease-in-out infinite;
}

.orange-circle-1 {
    width: 60px;
    height: 60px;
    background-color: rgba(255, 87, 34, 0.15);
    top: 30%;
    left: 15%;
    animation: float 10s ease-in-out infinite reverse;
}

.orange-circle-2 {
    width: 30px;
    height: 30px;
    background-color: rgba(255, 87, 34, 0.2);
    bottom: 30%;
    left: 10%;
    animation: floatRotate 15s ease-in-out infinite reverse;
}

.orange-circle-3 {
    width: 20px;
    height: 20px;
    background-color: rgba(255, 87, 34, 0.25);
    top: 60%;
    right: 30%;
    animation: orangePulse 4s infinite;
}

/* === Material Icons === */
.material-icons {
    font-family: 'Material Icons' !important;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--primary-color);
}

/* === Animations === */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 22, 96, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(0, 22, 96, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 22, 96, 0); }
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes borderPulse {
    0% { border-color: var(--primary-light); }
    50% { border-color: var(--accent-color); }
    100% { border-color: var(--primary-light); }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* === Wrapper === */
.login-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: transparent;
    padding: 0.5rem;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

/* === Container === */
.login-container {
    max-width: 1000px;
    width: 95%;
    max-height: 90vh;
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    position: relative;
    z-index: 1;
    animation: fadeIn 0.8s ease-out;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.login-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* === Grid Layout === */
.login-grid {
    display: grid;
    grid-template-columns: 42% 58%;
    height: 100%;
}

/* === Logo Section === */
.logo-section {
    background: #f0f4f9;
    color: var(--primary-color);
    padding: 3rem 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
    height: 100%;
}

/* Logo circle */
.logo-circle {
    width: 120px;
    height: 120px;
    background: #fff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.03);
    animation: float 6s ease-in-out infinite;
    position: relative;
}

.logo-circle::after {
    content: '';
    position: absolute;
    width: 110%;
    height: 110%;
    border-radius: 50%;
    border: 2px solid transparent;
    top: -5%;
    left: -5%;
    animation: borderPulse 3s infinite;
}

/* Company name */
.company-name {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Welcome text */
.welcome-text {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

/* Orange bottom border */
.orange-bottom-border {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-color), var(--accent-light), var(--accent-color));
    background-size: 200% 100%;
    animation: shimmer 3s infinite linear;
}

.logo-image {
    width: 80px;
    height: auto;
    max-width: 100%;
    animation: orangeGlow 3s infinite;
}

.logo-text {
    font-size: 1.4rem; /* Reduced font size */
    line-height: 1.4; /* Reduced line height */
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    font-weight: 700;
    margin-bottom: 1rem; /* Reduced margin */
    animation: slideUp 1s ease-in-out 0.5s both;
    position: relative;
    z-index: 2;
}

.logo-text span {
    display: inline-block;
    animation: slideInLeft 0.5s ease-out forwards;
    opacity: 0;
}

.logo-text span:nth-child(1) { animation-delay: 0.6s; }
.logo-text span:nth-child(2) { animation-delay: 0.7s; }
.logo-text span:nth-child(3) { animation-delay: 0.8s; }
.logo-text span:nth-child(4) { animation-delay: 0.9s; }
.logo-text span:nth-child(5) { animation-delay: 1.0s; }
.logo-text span:nth-child(6) { animation-delay: 1.1s; }
.logo-text span:nth-child(7) { animation-delay: 1.2s; }
.logo-text span:nth-child(8) { animation-delay: 1.3s; }
.logo-text span:nth-child(9) { animation-delay: 1.4s; }
.logo-text span:nth-child(10) { animation-delay: 1.5s; }

.tagline {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-top: 0.5rem;
    font-weight: 600;
    animation: slideUp 1s ease-in-out 1.2s both;
    position: relative;
    z-index: 2;
    color: rgba(255, 255, 255, 0.95);
    letter-spacing: 0.5px;
}

/* Platform description */
.platform-description {
    margin: 0.75rem 0; /* Reduced margin */
    padding: 0 1rem;
    animation: fadeIn 1.5s ease-in-out 1.5s both;
    position: relative;
    z-index: 2;
}

.platform-description p {
    font-size: 0.85rem; /* Reduced font size */
    line-height: 1.4; /* Reduced line height */
    color: rgba(255, 255, 255, 0.85);
    text-align: center;
    font-weight: 400;
    max-width: 90%;
    margin: 0 auto;
}

/* Feature highlights */
.feature-highlights {
    display: flex;
    flex-direction: column;
    gap: 0.75rem; /* Reduced gap */
    width: 100%;
    max-width: 90%;
    margin: 0.75rem auto 0; /* Reduced margin */
    animation: slideUp 1s ease-in-out 1.8s both;
    position: relative;
    z-index: 2;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem; /* Reduced gap */
    background: rgba(255, 255, 255, 0.1);
    padding: 0.75rem; /* Reduced padding */
    border-radius: 10px; /* Reduced border radius */
    backdrop-filter: blur(5px);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--accent-color);
    opacity: 0.7;
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateX(5px) translateY(-2px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.25);
}

.feature-item:hover::before {
    width: 6px;
    opacity: 1;
}

.feature-icon {
    color: var(--accent-light);
    font-size: 1.5rem;
    background: rgba(255, 255, 255, 0.15);
    padding: 8px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.25);
}

.feature-content {
    flex: 1;
}

.feature-content h3 {
    font-size: 0.9rem; /* Reduced font size */
    font-weight: 700;
    color: var(--white);
    margin: 0 0 0.15rem; /* Reduced margin */
    letter-spacing: 0.5px;
}

.feature-content p {
    font-size: 0.75rem; /* Reduced font size */
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    line-height: 1.3; /* Reduced line height */
}

/* === Form Section === */
.form-section {
    padding: 3rem 3.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    background: #ffffff;
    height: 100%;
}

.login-form {
    width: 100%;
}

.form-content {
    max-width: 380px;
    margin: 0 auto;
}

.title {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    font-weight: 700;
    text-align: center;
}

.subtitle {
    color: var(--text-light);
    margin-bottom: 1.5rem; /* Reduced margin */
    font-size: 0.95rem; /* Reduced font size */
    animation: slideUp 0.6s 0.2s both;
    position: relative;
    padding-left: 10px; /* Reduced padding */
    border-left: 3px solid var(--accent-color);
    line-height: 1.4; /* Reduced line height */
    max-width: 90%;
}

/* === Form Fields === */
.form-field {
    margin-bottom: 1.5rem;
    position: relative;
    animation: slideUp 0.5s ease-out both;
}

.form-field:nth-child(2) {
    animation-delay: 0.1s;
}

.form-field:nth-child(3) {
    animation-delay: 0.2s;
}

.form-field label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.85rem;
}

.password-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.3rem;
}

.forgot-password-link {
    font-size: 0.65rem;
    color: var(--text-light);
    text-decoration: none;
}

.forgot-password-link:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    z-index: 1;
    font-size: 1rem;
}

input[type="email"],
input[type="password"],
input[type="text"] {
    width: 100%;
    padding: 12px 16px 12px 45px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 0.9rem;
    color: var(--text-color);
    background: #ffffff;
}

input[type="email"]:focus,
input[type="password"]:focus,
input[type="text"]:focus {
    border-color: var(--primary-color);
    outline: none;
}

.input-wrapper:focus-within .input-icon {
    color: var(--primary-color);
}

.floating-label {
    position: absolute;
    left: 54px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1rem;
    color: var(--text-light);
    pointer-events: none;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    background: transparent;
}

input:focus + .floating-label,
input:not(:placeholder-shown) + .floating-label {
    top: -12px;
    left: 18px;
    font-size: 0.85rem;
    background-color: transparent;
    padding: 4px 12px;
    color: var(--primary-color);
    font-weight: 700;
    letter-spacing: 0.5px;
    z-index: 5;
    transform: translateY(0);
    animation: labelFloat 0.3s ease forwards;
}

@keyframes labelFloat {
    0% { transform: translateY(10px); opacity: 0.8; }
    100% { transform: translateY(0); opacity: 1; }
}

.toggle-password {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-light);
    z-index: 2;
    padding: 0;
    font-size: 0.9rem;
}

.toggle-password:hover {
    color: var(--primary-color);
}

.forget-password-link {
    display: inline-block;
    text-align: right;
    margin-top: 0.75rem;
    font-size: 0.9rem;
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    padding-right: 22px;
    float: right;
}

.forget-password-link::after {
    content: '\2192';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    transition: all 0.3s ease;
}

.forget-password-link:hover {
    color: var(--accent-color);
    padding-right: 28px;
}

.forget-password-link:hover::after {
    right: -5px;
    color: var(--accent-color);
}

/* Password strength indicator */
.password-strength {
    height: 4px;
    width: 100%;
    background: var(--border-color);
    position: absolute;
    bottom: -8px;
    left: 0;
    border-radius: 2px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.password-strength-bar {
    height: 100%;
    width: 0;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transition: width 0.5s ease;
    border-radius: 2px;
}

/* === Buttons === */
.signin-btn {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    animation: slideUp 0.5s ease-out 0.3s both;
    transition: all 0.3s ease;
}

.signin-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.5s ease;
}

.signin-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 22, 96, 0.2);
}

.signin-btn:hover::before {
    left: 100%;
}

.signin-btn:active {
    transform: translateY(0);
}

.signin-btn:focus {
    outline: none;
}

/* === Divider === */
.divider-text {
    text-align: center;
    margin: 1.5rem 0;
    color: var(--text-light);
    font-size: 0.8rem;
    position: relative;
    animation: fadeIn 0.8s ease-out 0.4s both;
}

.divider-text::before,
.divider-text::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 40%;
    height: 1px;
    background-color: #eee;
}

.divider-text::before {
    left: 0;
}

.divider-text::after {
    right: 0;
}

/* === Social Login === */
.social-signup {
    display: flex;
    justify-content: center;
    gap: 0.8rem;
    margin-bottom: 1rem;
    animation: slideUp 0.5s ease-out 0.4s both;
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #ffffff;
    border-radius: 50%;
    border: 1px solid #e0e0e0;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 87, 34, 0.2) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.social-btn:hover {
    background-color: #f8f8f8;
    transform: translateY(-3px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    border-color: rgba(255, 87, 34, 0.3);
}

.social-btn:hover::before {
    opacity: 1;
}

.social-btn img {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
}

.social-btn:hover img {
    transform: scale(1.1);
}

.social-btn.google {
    color: var(--google-color);
    border-color: rgba(219, 68, 55, 0.3);
    background: linear-gradient(to bottom, white, rgba(219, 68, 55, 0.03));
}

.social-btn.google:hover {
    background: linear-gradient(to bottom, white, rgba(219, 68, 55, 0.08));
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(219, 68, 55, 0.2);
    border-color: rgba(219, 68, 55, 0.5);
}

.social-btn.github {
    color: var(--github-color);
    border-color: rgba(51, 51, 51, 0.3);
    background: linear-gradient(to bottom, white, rgba(51, 51, 51, 0.03));
}

.social-btn.github:hover {
    background: linear-gradient(to bottom, white, rgba(51, 51, 51, 0.08));
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(51, 51, 51, 0.2);
    border-color: rgba(51, 51, 51, 0.5);
}

/* === Sign Up Link === */
.signup-link {
    text-align: center;
    margin-top: 1.5rem;
    font-size: 0.8rem;
    color: var(--text-light);
    animation: slideUp 0.5s ease-out 0.5s both;
}

.signup-link a {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    margin-left: 4px;
    position: relative;
    transition: color 0.3s ease;
}

.signup-link a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--accent-color);
    transition: width 0.3s ease;
}

.signup-link a:hover {
    color: var(--accent-color);
}

.signup-link a:hover::after {
    width: 100%;
}

/* === Copyright === */
.copyright {
    text-align: center;
    margin-top: 1.2rem;
    font-size: 0.75rem;
    color: var(--text-light);
    opacity: 0.7;
    animation: slideUp 0.5s ease-out 0.6s both;
}

/* === Messages === */
.error-message {
    background-color: rgba(229, 62, 62, 0.08);
    color: var(--error-color);
    border-left: 4px solid var(--error-color);
    padding: 1.25rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    font-size: 0.95rem;
    animation: slideInLeft 0.5s ease-in-out;
    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.1);
    position: relative;
}

.error-message::before {
    content: '\26A0';
    position: absolute;
    left: -12px;
    top: -12px;
    background: var(--error-color);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);
}

.success-message {
    background-color: rgba(56, 161, 105, 0.08);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
    padding: 1.25rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    font-size: 0.95rem;
    animation: slideInLeft 0.5s ease-in-out;
    box-shadow: 0 4px 12px rgba(56, 161, 105, 0.1);
    position: relative;
}

.success-message::before {
    content: '\2713';
    position: absolute;
    left: -12px;
    top: -12px;
    background: var(--success-color);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(56, 161, 105, 0.3);
}

/* === Responsive Design === */
@media (max-width: 1200px) {
    .login-container {
        width: 95%;
    }
}

@media (max-width: 992px) {
    .login-grid {
        grid-template-columns: 1fr;
    }

    .logo-section {
        padding: 3rem;
    }

    .form-section {
        padding: 3rem;
    }

    .logo-section .shape-1,
    .logo-section .shape-2,
    .logo-section .shape-3 {
        display: none;
    }

    .logo-image {
        width: 180px;
        margin-bottom: 1.5rem;
    }

    .logo-text {
        font-size: 1.4rem;
        margin-bottom: 1.5rem;
    }

    .tagline {
        margin-bottom: 1rem;
    }
}

@media (max-width: 768px) {
    .login-wrapper {
        padding: 1.5rem;
    }

    .login-container {
        width: 100%;
        border-radius: 16px;
    }

    .logo-section {
        padding: 2.5rem 2rem;
    }

    .form-section {
        padding: 2.5rem 2rem;
    }

    .title {
        font-size: 2rem;
    }

    .social-signup {
        flex-direction: column;
        gap: 1rem;
    }

    .social-btn {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .login-wrapper {
        padding: 1rem;
    }

    .login-container {
        border-radius: 12px;
    }

    .logo-section {
        padding: 2rem 1.5rem;
    }

    .form-section {
        padding: 2rem 1.5rem;
    }

    .title {
        font-size: 1.75rem;
    }

    .input-wrapper {
        border-radius: 10px;
    }

    input {
        padding: 16px 16px 16px 50px;
        font-size: 1rem;
    }

    .input-icon {
        left: 16px;
        font-size: 1.25rem;
    }

    .signin-btn {
        padding: 16px;
        font-size: 1rem;
    }

    .social-btn {
        padding: 12px 20px;
        font-size: 0.9rem;
    }

    .social-btn img {
        width: 18px;
        height: 18px;
        margin-right: 10px;
    }

    .signup-link {
        padding: 1rem;
        font-size: 0.9rem;
    }
}

/* === Additional Animations === */
@keyframes orangePulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 87, 34, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(255, 87, 34, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 87, 34, 0); }
}

@keyframes floatRotate {
    0% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
    100% { transform: translateY(0) rotate(0deg); }
}

@keyframes orangeGlow {
    0% { filter: drop-shadow(0 0 2px rgba(255, 87, 34, 0.3)); }
    50% { filter: drop-shadow(0 0 5px rgba(255, 87, 34, 0.6)); }
    100% { filter: drop-shadow(0 0 2px rgba(255, 87, 34, 0.3)); }
}